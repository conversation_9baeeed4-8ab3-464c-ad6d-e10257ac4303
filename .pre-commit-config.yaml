default_stages: [pre-commit, pre-push, manual]

repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: check-symlinks
      - id: destroyed-symlinks
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
        args: [--allow-multiple-documents]
      - id: check-toml
      - id: check-ast
      - id: check-added-large-files
      - id: check-merge-conflict
      - id: check-shebang-scripts-are-executable
      - id: detect-private-key
      - id: debug-statements
      - id: no-commit-to-branch
  - repo: https://github.com/PyCQA/isort
    rev: 5.13.2
    hooks:
      - id: isort
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.11.7
    hooks:
      - id: ruff
        args: [--select=F401, --fixable=F401]
        files: ^(benchmark/|docs/|examples/)
        exclude: \.ipynb$
  - repo: https://github.com/psf/black
    rev: 24.10.0
    hooks:
      - id: black-jupyter
  - repo: https://github.com/codespell-project/codespell
    rev: v2.4.1
    hooks:
      - id: codespell
        additional_dependencies: ['tomli']
        args: ['--toml', 'python/pyproject.toml', '-L', 'cann']
        exclude: |
          (?x)^(
            test/srt/test_reasoning_parser\.py|
            docs/advanced_features/vlm_query\.ipynb
          )$
  - repo: https://github.com/pre-commit/mirrors-clang-format
    rev: v18.1.8
    hooks:
    - id: clang-format
      types_or: [c++, cuda]
      args: [--style=file, --verbose]
  - repo: https://github.com/kynan/nbstripout
    rev: 0.8.1
    hooks:
      - id: nbstripout
        args:
          - '--keep-output'
          - '--extra-keys=metadata.kernelspec metadata.language_info.version'
