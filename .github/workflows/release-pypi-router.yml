# Reference: https://github.com/openai/tiktoken/blob/63527649963def8c759b0f91f2eb69a40934e468/.github/workflows/build_wheels.yml#L1

name: Release SGLang Router to PyPI

on:
  push:
    branches:
      - main
    paths:
      - sgl-router/pyproject.toml
  workflow_dispatch:

jobs:
  build:
    name: Build on ${{ matrix.os }} (${{ matrix.target }})
    runs-on: ${{ matrix.os }}-latest
    strategy:
      fail-fast: false
      matrix:
        include:
          - os: ubuntu
            target: x86_64

    steps:
      - uses: actions/checkout@v4
        with:
          path: sglang-repo

      - name: Move sgl-router folder to root and delete sglang-repo
        run: |
          mv sglang-repo/sgl-router/* .
          rm -rf sglang-repo
          ls -alt

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install build dependencies
        run: |
          python -m pip install -U pip
          python -m pip install build twine auditwheel

      - name: Build package
        uses: pypa/cibuildwheel@v2.21.3
        env:
          CIBW_BUILD: "cp38-manylinux_x86_64 cp39-manylinux_x86_64 cp310-manylinux_x86_64 cp311-manylinux_x86_64 cp312-manylinux_x86_64"
          CIBW_BEFORE_ALL: |
            yum update && yum install -y openssl-devel && curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
          CIBW_ENVIRONMENT: "PATH=$HOME/.cargo/bin:$PATH"

      - name: List built packages
        run: ls -lh wheelhouse/

      - name: Check packages
        run: twine check --strict wheelhouse/*

      - uses: actions/upload-artifact@v4
        with:
          name: packages-${{ matrix.os }}-${{ matrix.target }}
          path: wheelhouse/

  build-sdist:
    name: Build SDist
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          path: sglang-repo

      - name: Move sgl-router folder to root, copy the license file, and delete sglang-repo
        run: |
          mv sglang-repo/sgl-router/* .
          mv sglang-repo/LICENSE .
          rm -rf sglang-repo
          ls -alt

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Build SDist
        run: |
          pip install build
          python -m pip install -U packaging
          python -m build --sdist

      - uses: actions/upload-artifact@v4
        with:
          name: sdist
          path: dist/*.tar.gz

  upload:
    name: Upload to PyPI
    if: github.repository == 'sgl-project/sglang'  # Ensure this job only runs for the sgl-project/sglang repository
    needs: [build, build-sdist]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/download-artifact@v4
        with:
          path: dist
          merge-multiple: true

      - name: Upload to PyPI
        env:
          TWINE_USERNAME: __token__
          TWINE_PASSWORD: ${{ secrets.PYPI_TOKEN_ROUTER }}
        run: |
          pip install twine
          twine upload dist/* --verbose
