name: Nightly Test (AMD)

on:
  schedule:
    - cron: '0 0 * * *'
  push:
    branches:
      - main
    paths:
      - "python/sglang/version.py"
  workflow_dispatch:

concurrency:
  group: nightly-test-amd-${{ github.ref }}
  cancel-in-progress: true

jobs:
  nightly-test:
    if: github.repository == 'sgl-project/sglang' || github.event_name == 'pull_request'
    strategy:
      matrix:
        runner: [linux-mi300-gpu-2, linux-mi325-gpu-2-nightly]
    runs-on: ${{matrix.runner}}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup docker
        run: |
          touch github_summary.md
          bash scripts/ci/amd_ci_start_container.sh
        env:
          GITHUB_WORKSPACE: ${{ github.workspace }}

      - name: Install dependencies
        run: bash scripts/ci/amd_ci_install_dependency.sh

      - name: Nightly Test
        run: |
          bash scripts/ci/amd_ci_exec.sh -e GITHUB_STEP_SUMMARY="/sglang-checkout/github_summary.md" python3 run_suite.py --suite nightly-amd --timeout-per-file 7200
          echo "$(<github_summary.md )" >> $GITHUB_STEP_SUMMARY
