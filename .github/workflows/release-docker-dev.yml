name: Build Development Docker Image

on:
  workflow_dispatch:
  schedule:
    - cron: '0 0 * * *'

jobs:
  build-dev:
    if: ${{ github.repository == 'sgl-project/sglang' }}
    runs-on: ubuntu-22.04
    strategy:
      matrix:
        variant:
          - version: 12.6.1
            type: all
            tag: dev
          - version: 12.8.1
            type: blackwell
            tag: blackwell
          - version: 12.9.1
            type: blackwell
            tag: b200-cu129

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Free disk space
        uses: jlumbroso/free-disk-space@main
        with:
          tool-cache: false
          docker-images: false
          android: true
          dotnet: true
          haskell: true
          large-packages: true
          swap-storage: false

      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and Push Dev Image
        run: |
          docker buildx build --output type=image,compression=zstd . -f docker/Dockerfile --build-arg CUDA_VERSION=${{ matrix.variant.version }} --build-arg BUILD_TYPE=${{ matrix.variant.type }} --build-arg CMAKE_BUILD_PARALLEL_LEVEL=$(nproc) -t lmsysorg/sglang:${{ matrix.variant.tag }} --no-cache
          docker push lmsysorg/sglang:${{ matrix.variant.tag }}
