name: Release Docker Images Nightly (AMD)
on:
  workflow_dispatch:
  schedule:
    - cron: '0 13 * * *'

concurrency:
  # A PR number if a pull request and otherwise the commit hash. This cancels
  # queued and in-progress runs for the same PR (presubmit) or commit
  # (postsubmit). The workflow name is prepended to avoid conflicts between
  # different workflows.
  group: ${{ github.workflow }}-${{ github.event.number || github.sha }}
  cancel-in-progress: true

jobs:
  publish:
    if: github.repository == 'sgl-project/sglang'
    runs-on: amd-docker-scale
    environment: 'prod'
    strategy:
      matrix:
        gpu_arch: ['gfx942', 'gfx950']
        build_type: ['all', 'srt']
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: "Set Date"
        run: |
          echo "DATE=$(date +%Y%m%d)" >> $GITHUB_ENV

      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_AMD_USERNAME }}
          password: ${{ secrets.DOCKERHUB_AMD_TOKEN }}

      - name: Build and Push
        run: |
          version=$(cat python/sglang/version.py | cut -d'"' -f2)

          if [ "${{ matrix.gpu_arch }}" = "gfx942" ]; then
            rocm_tag="rocm630-mi30x"
          elif [ "${{ matrix.gpu_arch }}" = "gfx950" ]; then
            rocm_tag="rocm700-mi35x"
          else
            echo "Unsupported gfx arch"
            exit 1
          fi

          tag=v${version}-${rocm_tag}

          if [ "${{ matrix.build_type }}" = "all" ]; then
            tag_suffix=""
          elif [ "${{ matrix.build_type }}" = "srt" ]; then
            tag_suffix="-srt"
          else
            echo "Unsupported build type"
            exit 1
          fi

          docker build . -f docker/Dockerfile.rocm --build-arg BUILD_TYPE=${{ matrix.build_type }} --build-arg GPU_ARCH=${{ matrix.gpu_arch }} -t rocm/sgl-dev:${tag}-${{ env.DATE }}${tag_suffix} --no-cache
          docker push rocm/sgl-dev:${tag}-${{ env.DATE }}${tag_suffix}
