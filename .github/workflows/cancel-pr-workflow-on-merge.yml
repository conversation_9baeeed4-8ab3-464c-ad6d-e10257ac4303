name: Cancel PR Workflows on Merge

on:
  pull_request_target:
    types:
      - closed

permissions:
  actions: write

jobs:
  cancel:
    if: github.event.pull_request.merged == true
    runs-on: ubuntu-latest
    steps:
      - name: Cancel Previous Runs
        uses: styfle/cancel-workflow-action@0.12.1
        with:
          workflow_id: all
          access_token: ${{ secrets.GITHUB_TOKEN }}
          ignore_sha: true
          pr_number: ${{ github.event.pull_request.number }}
