name: Nightly Test

on:
  schedule:
    - cron: '0 0 * * *'
  push:
    branches:
      - main
    paths:
      - "python/sglang/version.py"
  workflow_dispatch:

concurrency:
  group: nightly-test-${{ github.ref }}
  cancel-in-progress: true

jobs:
  nightly-test:
    if: github.repository == 'sgl-project/sglang' || github.event_name == 'pull_request'
    runs-on: 2-gpu-runner
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install dependencies
        run: |
          bash scripts/ci/ci_install_dependency.sh

      - name: Run test
        timeout-minutes: 120
        run: |
          cd test/srt
          python3 run_suite.py --suite nightly --timeout-per-file 3600
