name: Release Docker Images (AMD)
on:
  push:
    branches:
      - main
    paths:
      - "python/sglang/version.py"
  workflow_dispatch:

jobs:
  publish:
    if: github.repository == 'sgl-project/sglang'
    runs-on: amd-docker-scale
    environment: 'prod'
    strategy:
      matrix:
        gpu_arch: ['gfx942', 'gfx950']
        build_type: ['all', 'srt']
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and Push
        run: |
          version=$(cat python/sglang/version.py | cut -d'"' -f2)

          if [ "${{ matrix.gpu_arch }}" = "gfx942" ]; then
            rocm_tag="rocm630-mi30x"
          elif [ "${{ matrix.gpu_arch }}" = "gfx950" ]; then
            rocm_tag="rocm700-mi35x"
          else
            echo "Unsupported gfx arch"
            exit 1
          fi

          tag=v${version}-${rocm_tag}

          if [ "${{ matrix.build_type }}" = "all" ]; then
            tag_suffix=""
          elif [ "${{ matrix.build_type }}" = "srt" ]; then
            tag_suffix="-srt"
          else
            echo "Unsupported build type"
            exit 1
          fi

          docker build . -f docker/Dockerfile.rocm --build-arg BUILD_TYPE=${{ matrix.build_type }} --build-arg GPU_ARCH=${{ matrix.gpu_arch }} -t lmsysorg/sglang:${tag}${tag_suffix} --no-cache
          docker push lmsysorg/sglang:${tag}${tag_suffix}
