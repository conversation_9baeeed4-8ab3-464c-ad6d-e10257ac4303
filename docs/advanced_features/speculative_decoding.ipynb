{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Speculative Decoding\n", "\n", "SGLang now provides an EAGLE-based (EAGLE-2/EAGLE-3) speculative decoding option. Our implementation aims to maximize speed and efficiency and is considered to be among the fastest in open-source LLM engines.\n", "\n", "### Performance Highlights\n", "\n", "Please see below for the huge improvements on throughput for LLaMA-Instruct 3.1 8B tested on MT bench that can be achieved via EAGLE3 decoding.\n", "For further details please see the [EAGLE3 paper](https://arxiv.org/pdf/2503.01840).\n", "\n", "| Method | Throughput (tokens/s) |\n", "|--------|----------------|\n", "| SGLang (w/o speculative, 1x H100) | 158.34 tokens/s |\n", "| SGLang + EAGLE-2 (1x H100) | 244.10 tokens/s |\n", "| SGLang + EAGLE-3 (1x H100) | 373.25 tokens/s |"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## EAGLE Decoding\n", "\n", "To enable EAGLE speculative decoding the following parameters are relevant:\n", "* `speculative_draft_model_path`: Specifies draft model. This parameter is required.\n", "* `speculative_num_steps`: Depth of autoregressive drafting. Increases speculation range but risks rejection cascades. Default is 5.\n", "* `speculative_eagle_topk`: Branching factor per step. Improves candidate diversity, will lead to higher acceptance rate, but more lead to higher memory/compute consumption. Default is 4.\n", "* `speculative_num_draft_tokens`: Maximum parallel verification capacity. Allows deeper tree evaluation but will lead to higher GPU memory usage. Default is 8.\n", "\n", "These parameters are the same for EAGLE-2 and EAGLE-3.\n", "\n", "You can find the best combinations of these parameters with [bench_speculative.py](https://github.com/sgl-project/sglang/blob/main/scripts/playground/bench_speculative.py).\n", "\n", "In the documentation below, we set `--cuda-graph-max-bs` to be a small value for faster engine startup. For your own workloads, please tune the above parameters together with `--cuda-graph-max-bs`, `--max-running-requests`, `--mem-fraction-static` for the best performance. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### EAGLE-2 decoding\n", "\n", "You can enable EAGLE-2 decoding by setting `--speculative_algorithm EAGLE` and choosing an appropriate model."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sglang.test.doc_patch import launch_server_cmd\n", "from sglang.utils import wait_for_server, print_highlight, terminate_process\n", "\n", "import openai"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["server_process, port = launch_server_cmd(\n", "    \"\"\"\n", "python3 -m sglang.launch_server --model meta-llama/Llama-2-7b-chat-hf  --speculative-algorithm EAGLE \\\n", "    --speculative-draft-model-path lmsys/sglang-EAGLE-llama2-chat-7B --speculative-num-steps 3 \\\n", "    --speculative-eagle-topk 4 --speculative-num-draft-tokens 16 --cuda-graph-max-bs 8\n", "\"\"\"\n", ")\n", "\n", "wait_for_server(f\"http://localhost:{port}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["client = openai.Client(base_url=f\"http://127.0.0.1:{port}/v1\", api_key=\"None\")\n", "\n", "response = client.chat.completions.create(\n", "    model=\"meta-llama/Llama-2-7b-chat-hf\",\n", "    messages=[\n", "        {\"role\": \"user\", \"content\": \"List 3 countries and their capitals.\"},\n", "    ],\n", "    temperature=0,\n", "    max_tokens=64,\n", ")\n", "\n", "print_highlight(f\"Response: {response}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["terminate_process(server_process)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### EAGLE-2 Decoding with `torch.compile`\n", "\n", "You can also enable `torch.compile` for further optimizations and optionally set `--torch-compile-max-bs`:\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["server_process, port = launch_server_cmd(\n", "    \"\"\"\n", "python3 -m sglang.launch_server --model meta-llama/Llama-2-7b-chat-hf  --speculative-algorithm EAGLE \\\n", "    --speculative-draft-model-path lmsys/sglang-EAGLE-llama2-chat-7B --speculative-num-steps 5 \\\n", "        --speculative-eagle-topk 8 --speculative-num-draft-tokens 64 --mem-fraction 0.6 \\\n", "            --enable-torch-compile --torch-compile-max-bs 2\n", "\"\"\"\n", ")\n", "\n", "wait_for_server(f\"http://localhost:{port}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["client = openai.Client(base_url=f\"http://127.0.0.1:{port}/v1\", api_key=\"None\")\n", "\n", "response = client.chat.completions.create(\n", "    model=\"meta-llama/Llama-2-7b-chat-hf\",\n", "    messages=[\n", "        {\"role\": \"user\", \"content\": \"List 3 countries and their capitals.\"},\n", "    ],\n", "    temperature=0,\n", "    max_tokens=64,\n", ")\n", "\n", "print_highlight(f\"Response: {response}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["terminate_process(server_process)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### EAGLE-2 Decoding via Frequency-Ranked Speculative Sampling\n", "\n", "By employing a truncated high-frequency token vocabulary in the draft model, Eagle speculative decoding reduces `lm_head` computational overhead while accelerating the pipeline without quality degradation. For more details, checkout [the paper](https://arxiv.org/pdf/arXiv:2502.14856).\n", "\n", "In our implementation, set `--speculative-token-map` to enable the optimization. You can get the high-frequency token in FR-Spec from [this model](https://huggingface.co/thunlp/LLaMA3-Instruct-8B-FR-Spec). Or you can obtain high-frequency token by directly downloading these token from [this repo](https://github.com/thunlp/FR-Spec/tree/main?tab=readme-ov-file#prepare-fr-spec-vocabulary-subset).\n", "\n", "Thanks for the contribution from [<PERSON><PERSON>](https://github.com/Achazwl) and [Zhousx](https://github.com/<PERSON>-sx). "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["server_process, port = launch_server_cmd(\n", "    \"\"\"\n", "python3 -m sglang.launch_server --model meta-llama/Meta-Llama-3-8B-Instruct --speculative-algorithm EAGLE \\\n", "    --speculative-draft-model-path lmsys/sglang-EAGLE-LLaMA3-Instruct-8B --speculative-num-steps 5 \\\n", "    --speculative-eagle-topk 8 --speculative-num-draft-tokens 64 --speculative-token-map thunlp/LLaMA3-Instruct-8B-FR-Spec/freq_32768.pt \\\n", "    --mem-fraction 0.7 --cuda-graph-max-bs 2 --dtype float16 \n", "\"\"\"\n", ")\n", "\n", "wait_for_server(f\"http://localhost:{port}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["client = openai.Client(base_url=f\"http://127.0.0.1:{port}/v1\", api_key=\"None\")\n", "\n", "response = client.chat.completions.create(\n", "    model=\"meta-llama/Meta-Llama-3-8B-Instruct\",\n", "    messages=[\n", "        {\"role\": \"user\", \"content\": \"List 3 countries and their capitals.\"},\n", "    ],\n", "    temperature=0,\n", "    max_tokens=64,\n", ")\n", "\n", "print_highlight(f\"Response: {response}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["terminate_process(server_process)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### EAGLE-3 Decoding\n", "\n", "You can enable EAGLE-3 decoding by setting `--speculative_algorithm EAGLE3` and choosing an appropriate model."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["server_process, port = launch_server_cmd(\n", "    \"\"\"\n", "python3 -m sglang.launch_server --model meta-llama/Llama-3.1-8B-Instruct  --speculative-algorithm EAGLE3 \\\n", "    --speculative-draft-model-path jamesliu1/sglang-EAGLE3-Llama-3.1-Instruct-8B --speculative-num-steps 5 \\\n", "        --speculative-eagle-topk 8 --speculative-num-draft-tokens 32 --mem-fraction 0.6 \\\n", "        --cuda-graph-max-bs 2 --dtype float16\n", "\"\"\"\n", ")\n", "\n", "wait_for_server(f\"http://localhost:{port}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["client = openai.Client(base_url=f\"http://127.0.0.1:{port}/v1\", api_key=\"None\")\n", "\n", "response = client.chat.completions.create(\n", "    model=\"meta-llama/Meta-Llama-3.1-8B-Instruct\",\n", "    messages=[\n", "        {\"role\": \"user\", \"content\": \"List 3 countries and their capitals.\"},\n", "    ],\n", "    temperature=0,\n", "    max_tokens=64,\n", ")\n", "\n", "print_highlight(f\"Response: {response}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["terminate_process(server_process)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Multi Token Prediction\n", "\n", "We support [MTP(Multi-Token Prediction)](https://arxiv.org/pdf/2404.19737) in SGLang by using speculative decoding. We use Xiaomi/MiMo-7B-RL model as example here (deepseek mtp usage refer to [deepseek doc](../references/deepseek.md#multi-token-prediction))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["server_process, port = launch_server_cmd(\n", "    \"\"\"\n", "    python3 -m sglang.launch_server --model-path XiaomiMiMo/MiMo-7B-RL --host 0.0.0.0 --trust-remote-code \\\n", "    --speculative-algorithm EAGLE --speculative-num-steps 1 --speculative-eagle-topk 1 --speculative-num-draft-tokens 2 \\\n", "    --mem-fraction 0.5\n", "\"\"\"\n", ")\n", "\n", "wait_for_server(f\"http://localhost:{port}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "\n", "url = f\"http://localhost:{port}/v1/chat/completions\"\n", "\n", "data = {\n", "    \"model\": \"XiaomiMiMo/MiMo-7B-RL\",\n", "    \"messages\": [{\"role\": \"user\", \"content\": \"What is the capital of France?\"}],\n", "}\n", "\n", "response = requests.post(url, json=data)\n", "print_highlight(response.json())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["terminate_process(server_process)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## References\n", "\n", "EAGLE process is as follows:\n", "\n", "- Within EAGLE the draft model predicts the next feature vector, i.e. the last hidden state of the original LLM, using the feature sequence $(f_1, ..., f_k)$ and the token sequence $(t_2, ..., t_{k+1})$. \n", "- The next token is then sampled from $p_{k+2}=\\text{LMHead}(f_{k+1})$. Afterwards, the two sequences are extended in a tree style—branching out multiple potential continuations, with the branching factor per step controlled by the `speculative_eagle_topk` parameter—to ensure a more coherent connection of context, and are given as input again.\n", "- EAGLE-2 additionally uses the draft model to evaluate how probable certain branches in the draft tree are, dynamically stopping the expansion of unlikely branches. After the expansion phase, reranking is employed to select only the top `speculative_num_draft_tokens` final nodes as draft tokens.\n", "- EAGLE-3 removes the feature prediction objective, incorporates low and mid-layer features, and is trained in an on-policy manner.\n", "\n", "This enhances drafting accuracy by operating on the features instead of tokens for more regular inputs and passing the tokens from the next timestep additionally to minimize randomness effects from sampling. Furthermore the dynamic adjustment of the draft tree and selection of reranked final nodes increases acceptance rate of draft tokens further. For more details see [EAGLE-2](https://arxiv.org/abs/2406.16858) and [EAGLE-3](https://arxiv.org/abs/2503.01840) paper.\n", "\n", "\n", "For guidance how to train your own EAGLE model please see the [EAGLE repo](https://github.com/SafeAILab/EAGLE/tree/main?tab=readme-ov-file#train)."]}], "metadata": {"language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3"}}, "nbformat": 4, "nbformat_minor": 2}