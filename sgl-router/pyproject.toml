[build-system]
requires = ["setuptools>=45", "wheel", "setuptools-rust>=1.5.2"]
build-backend = "setuptools.build_meta"

[project]
name = "sglang-router"
version = "0.1.9"
description = "High-performance Rust-based load balancer for SGLang with multiple routing algorithms and prefill-decode disaggregation support"
authors = [{name = "<PERSON> H<PERSON>", email = "<EMAIL>"}]
requires-python = ">=3.8"
readme = "README.md"
license = { text = "Apache-2.0" }
classifiers = [
    "Programming Language :: Python :: Implementation :: CPython",
    "Programming Language :: Rust",
    "Programming Language :: Python :: 3",
]

[project.optional-dependencies]
dev = [
    "requests>=2.25.0",
]

# https://github.com/PyO3/setuptools-rust?tab=readme-ov-file
[tool.setuptools.packages]
find = { where = ["py_src"] }

# workaround for https://github.com/pypa/twine/issues/1216
[tool.setuptools]
license-files = []

[[tool.setuptools-rust.ext-modules]]
target = "sglang_router_rs"
path = "Cargo.toml"
binding = "PyO3"
